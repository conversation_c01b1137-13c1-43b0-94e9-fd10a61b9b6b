document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const sidebar = document.getElementById('sidebar');
    const toggleSidebarBtn = document.getElementById('toggleSidebar');
    const toggleSidebarMobile = document.getElementById('toggleSidebarMobile');
    const messagesContainer = document.getElementById('messages');
    const userInput = document.getElementById('userInput');
    const sendButton = document.getElementById('sendButton');
    const dictateButton = document.getElementById('dictateButton');
    const sessionList = document.getElementById('sessionList');
    const newSessionBtn = document.getElementById('newSession');

    // State
    let currentSession = null;
    let isListening = false;
    let recognition = null;
    let isCollapsed = false;

    // Initialize
    loadSidebarState();
    initializeFirstSession();
    setupEventListeners();

    // Sidebar Functions
    function loadSidebarState() {
        const savedState = localStorage.getItem('sidebarCollapsed');
        if (savedState === 'true') {
            toggleSidebar();
        }
    }

    function toggleSidebar() {
        isCollapsed = !isCollapsed;
        sidebar.classList.toggle('collapsed');
        toggleSidebarBtn.innerHTML = isCollapsed
            ? '<i class="fas fa-chevron-right"></i>'
            : '<i class="fas fa-chevron-left"></i>';
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    }

    // Session Management
    function initializeFirstSession() {
        fetch('http://localhost:3001/api/init')
            .then(res => res.json())
            .then(data => {
                messagesContainer.innerHTML = '';
                addMessage(data.message, false);

                if (data.sessions && data.sessions.length > 0) {
                    updateSessionList(data.sessions);
                }

                enableInput();
            });
    }

    function updateSessionList(sessions) {
        sessionList.innerHTML = '';
        sessions.forEach(session => {
            const item = document.createElement('div');
            item.className = 'session-item';
            item.textContent = session.name;
            item.dataset.id = session.id;
            item.addEventListener('click', () => loadSession(session.id));
            sessionList.appendChild(item);
        });
        highlightActiveSession();
    }

    function loadSession(sessionId) {
        messagesContainer.innerHTML = '';
        addMessage("Loading session...", false);
        disableInput();

        fetch(`http://localhost:3001/api/session/${sessionId}`)
            .then(res => res.json())
            .then(messages => {
                messagesContainer.innerHTML = '';
                if (messages.length === 0) {
                    addMessage("Welcome! Ask me anything about mathematics.", false);
                } else {
                    messages.forEach(msg => {
                        addMessage(msg.input, true);
                        addMessage(msg.output, false);
                    });
                }
                currentSession = sessionId;
                highlightActiveSession();
            })
            .catch(error => {
                addMessage(`Error loading session: ${error.message}`, false);
            })
            .finally(() => {
                enableInput();
                scrollToBottom();
            });
    }

    function createNewSession() {
        messagesContainer.innerHTML = '';
        currentSession = null;
        addMessage("Welcome! What would you like to discuss today?", false);
        enableInput();
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    function highlightActiveSession() {
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.toggle('active', item.dataset.id === currentSession);
        });
    }

    // Message Functions
    function sendMessage() {
        const message = userInput.value.trim();
        if (!message) return;

        disableInput();
        addMessage(message, true);
        userInput.value = '';
        const loadingMessage = addMessage("", false, true);

        fetch('http://localhost:3001/api/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                input_value: message,
                session_id: currentSession
            })
        })
        .then(res => res.json())
        .then(data => {
            loadingMessage.innerHTML = formatResponse(data.output);
            if (data.isNewSession) {
                currentSession = data.sessionId;
                fetch('http://localhost:3001/api/sessions')
                    .then(res => res.json())
                    .then(sessions => updateSessionList(sessions));
            }
        })
        .catch(error => {
            loadingMessage.innerHTML = `Error: ${error.message}`;
        })
        .finally(() => {
            enableInput();
            scrollToBottom();
        });
    }

    function addMessage(text, isUser, isLoading = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;

        if (isLoading) {
            messageDiv.innerHTML = `
                <div class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            `;
        } else {
            messageDiv.innerHTML = formatResponse(text);
        }

        messagesContainer.appendChild(messageDiv);
        scrollToBottom();
        return messageDiv;
    }

    function formatResponse(text) {
        if (!text) return '';

        // Debug: Log the original text received
        console.log('formatResponse received text:', {
            text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
            length: text.length,
            type: typeof text,
            hasNewlines: text.includes('\n'),
            newlineCount: text.split('\n').length
        });

        // Simple approach: detect unformatted code and wrap it, then handle everything else normally

        // Universal code detection - works for ANY programming language
        const codeIndicators = [
            // Common programming patterns
            /\w+\s*=\s*\w+/,           // variable assignment
            /\w+\(\s*.*\s*\)/,         // function calls
            /\{[\s\S]*\}/,             // code blocks with braces
            /\w+\.\w+/,                // object/method access
            /import\s+\w+/,            // imports
            /from\s+\w+/,              // from imports
            /def\s+\w+/,               // function definitions
            /class\s+\w+/,             // class definitions
            /function\s+\w+/,          // function keyword
            /const\s+\w+/,             // const declarations
            /let\s+\w+/,               // let declarations
            /var\s+\w+/,               // var declarations
            /if\s*\(/,                 // if statements
            /for\s*\(/,                // for loops
            /while\s*\(/,              // while loops
            /\w+::\w+/,                // C++ scope resolution
            /\w+\[\w*\]/,              // array/list access
            /@\w+/,                    // decorators/annotations
            /\$\w+/,                   // PHP variables
            /\w+\s*->\s*\w+/,          // arrow operators
            /\w+\s*=>\s*\w+/,          // arrow functions
            /\w+\s*::\s*\w+/,          // static access
        ];

        let codeIndicatorCount = 0;
        codeIndicators.forEach(pattern => {
            if (pattern.test(text)) codeIndicatorCount++;
        });

        const isLongLine = text.length > 100;  // Reduced threshold
        const hasLimitedNewlines = text.split('\n').length < 6;  // Increased threshold
        const hasMultipleStatements = text.includes(';') && text.split(';').length > 2;  // Reduced threshold

        // More aggressive code detection
        const hasCodeKeywords = /\b(function|class|import|from|def|const|let|var|if|for|while|return)\b/.test(text);
        const hasCodeSymbols = /[{}();=]/.test(text) && text.split(/[{}();=]/).length > 3;

        // Debug logging
        console.log('Code detection analysis:', {
            text: text.substring(0, 100) + '...',
            codeIndicatorCount,
            isLongLine,
            hasLimitedNewlines,
            hasMultipleStatements,
            hasCodeKeywords,
            hasCodeSymbols,
            textLength: text.length,
            newlineCount: text.split('\n').length
        });

        // If it looks like unformatted code (ANY language), format it
        if ((codeIndicatorCount >= 2 || hasMultipleStatements || (hasCodeKeywords && hasCodeSymbols)) &&
            isLongLine && hasLimitedNewlines) {
            console.log('Applying code formatting...');
            const formattedCode = formatUniversalCode(text);
            text = `\`\`\`\n${formattedCode}\n\`\`\``;
        }
        // Fallback: If text has many code indicators but is already formatted, just wrap it
        else if (codeIndicatorCount >= 3 && !text.includes('```')) {
            console.log('Wrapping already-formatted code...');
            text = `\`\`\`\n${text}\n\`\`\``;
        }

        // Now process normally
        // Escape HTML
        text = escapeHtml(text);

        // Handle code blocks
        text = text.replace(/```(\w*)\n?([\s\S]*?)\n?```/g, (_, language, code) => {
            return `<div class="code-container">
                <div class="code-header">
                    <span>${language || 'code'}</span>
                    <button class="copy-btn" onclick="copyCodeToClipboard(this)">Copy</button>
                </div>
                <pre class="code-block">${code.trim()}</pre>
            </div>`;
        });

        // Handle inline code
        text = text.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

        // Handle markdown formatting
        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Handle line breaks - simple approach
        text = text.replace(/\n\n/g, '</p><p>');
        text = text.replace(/\n/g, '<br>');

        // Wrap in paragraphs if no code blocks
        if (!text.includes('<div class="code-container">')) {
            text = `<p>${text}</p>`;
        } else {
            // Handle mixed content more carefully
            const parts = text.split(/(<div class="code-container">[\s\S]*?<\/div>)/);
            let result = '';

            for (let i = 0; i < parts.length; i++) {
                if (parts[i].includes('code-container')) {
                    result += parts[i];
                } else if (parts[i].trim()) {
                    result += `<p>${parts[i]}</p>`;
                }
            }
            text = result;
        }

        return text;
    }

    // Universal code formatter - works for ALL programming languages
    function formatUniversalCode(code) {
        // Step 1: Add line breaks after common statement terminators
        let formatted = code
            // Semicolons (JavaScript, C++, Java, C#, etc.)
            .replace(/;(?!\s*[)}]|\s*$)/g, ';\n')
            // Opening braces (JavaScript, C++, Java, C#, etc.)
            .replace(/\{(?!\s*$)/g, '{\n')
            // Closing braces
            .replace(/([^{\n])\}/g, '$1\n}')
            // Commas in arrays/objects (but not in function parameters)
            .replace(/,(?!\s*[}\]]|\s*\))/g, ',\n')
            // Python colons (function/class/if definitions)
            .replace(/:(?!\s*$)/g, ':\n')
            // Common keywords that should start new lines
            .replace(/(\w)(import\s+)/g, '$1\n$2')
            .replace(/(\w)(from\s+)/g, '$1\n$2')
            .replace(/(\w)(def\s+)/g, '$1\n$2')
            .replace(/(\w)(class\s+)/g, '$1\n$2')
            .replace(/(\w)(function\s+)/g, '$1\n$2')
            .replace(/(\w)(if\s+)/g, '$1\n$2')
            .replace(/(\w)(for\s+)/g, '$1\n$2')
            .replace(/(\w)(while\s+)/g, '$1\n$2')
            .replace(/(\w)(else\s*)/g, '$1\nelse ')
            .replace(/(\w)(elif\s+)/g, '$1\nelif ')
            .replace(/(\w)(try\s*)/g, '$1\ntry ')
            .replace(/(\w)(catch\s*)/g, '$1\ncatch ')
            .replace(/(\w)(finally\s*)/g, '$1\nfinally ')
            .replace(/(\w)(const\s+)/g, '$1\nconst ')
            .replace(/(\w)(let\s+)/g, '$1\nlet ')
            .replace(/(\w)(var\s+)/g, '$1\nvar ')
            // Clean up multiple newlines
            .replace(/\n\s*\n\s*\n/g, '\n\n')
            .trim();

        // Step 2: Add proper indentation
        const lines = formatted.split('\n');
        let indentLevel = 0;
        const indentedLines = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) {
                indentedLines.push('');
                continue;
            }

            // Decrease indent for closing braces/keywords
            if (line.startsWith('}') || line.startsWith('else') ||
                line.startsWith('elif') || line.startsWith('catch') ||
                line.startsWith('finally')) {
                indentLevel = Math.max(0, indentLevel - 1);
            }

            // Reset indent for top-level constructs
            if (line.startsWith('import ') || line.startsWith('from ') ||
                line.startsWith('def ') || line.startsWith('class ') ||
                line.startsWith('function ') || line.match(/^(const|let|var)\s+\w+\s*=/)) {
                if (!line.includes('    ')) { // Only reset if not already indented
                    indentLevel = 0;
                }
            }

            // Add the line with current indentation
            indentedLines.push('    '.repeat(indentLevel) + line);

            // Increase indent after opening constructs
            if (line.endsWith('{') || line.endsWith(':') ||
                line.match(/(if|for|while|else|elif|try|catch|finally|function|def|class)\s*.*[{:]?$/)) {
                indentLevel++;
            }

            // Decrease indent after closing braces (for next iteration)
            if (line.startsWith('}')) {
                indentLevel = Math.max(0, indentLevel - 1);
            }
        }

        return indentedLines.join('\n');
    }

    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    // Voice Dictation
    function startDictation() {
        if (!('webkitSpeechRecognition' in window)) {
            alert('Speech recognition not supported in your browser');
            return;
        }

        recognition = new webkitSpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        let finalTranscript = '';
        let timeout;

        recognition.onstart = () => {
            isListening = true;
            dictateButton.classList.add('listening');
            userInput.placeholder = "Listening...";
            userInput.value = '';
        };

        recognition.onresult = (event) => {
            clearTimeout(timeout);

            let interimTranscript = '';
            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            userInput.value = finalTranscript + interimTranscript;

            timeout = setTimeout(() => {
                if (finalTranscript.trim()) {
                    sendMessage();
                }
                stopDictation();
            }, 2000);
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error', event.error);
            stopDictation();
        };

        recognition.onend = () => {
            stopDictation();
        };

        recognition.start();
    }

    function stopDictation() {
        if (recognition) {
            recognition.stop();
        }
        dictateButton.classList.remove('listening');
        isListening = false;
        userInput.placeholder = "Type your message...";
    }

    // UI Helpers
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    function enableInput() {
        userInput.disabled = false;
        sendButton.disabled = false;
        dictateButton.disabled = false;
        userInput.placeholder = "Type your message...";
        userInput.focus();
    }

    function disableInput() {
        userInput.disabled = true;
        sendButton.disabled = true;
        dictateButton.disabled = true;
        userInput.placeholder = "Processing...";
    }



    // Event Listeners
    function setupEventListeners() {
        toggleSidebarBtn.addEventListener('click', toggleSidebar);
        toggleSidebarMobile.addEventListener('click', toggleSidebar);

        sendButton.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        dictateButton.addEventListener('click', () => {
            isListening ? stopDictation() : startDictation();
        });

        newSessionBtn.addEventListener('click', createNewSession);
    }



    // Global function for copy button
    window.copyCodeToClipboard = async function(button) {
        const codeBlock = button.closest('.code-container').querySelector('.code-block');

        // Get the text content with preserved line breaks
        let textToCopy = codeBlock.textContent;

        const originalText = button.textContent;

        try {
            // Try modern Clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(textToCopy);
                button.textContent = 'Copied!';
            } else {
                // Fallback to older method
                const textarea = document.createElement('textarea');
                textarea.value = textToCopy;
                textarea.style.position = 'absolute';
                textarea.style.left = '-9999px';
                document.body.appendChild(textarea);
                textarea.select();

                const successful = document.execCommand('copy');
                document.body.removeChild(textarea);

                button.textContent = successful ? 'Copied!' : 'Failed';
            }

            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
            button.textContent = 'Failed';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        }
    };
});
